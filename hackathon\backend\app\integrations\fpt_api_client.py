import requests
from app.config import settings

class FPTApiClient:
    def __init__(self):
        self.base_url = settings.FPT_API_BASE
        self.headers = {"Authorization": f"Bearer {settings.FPT_API_KEY}"}

    def complete(self, messages, ai_model=None):
        payload = {
            "model": ai_model or settings.FPT_MODEL,
            "messages": messages,
            "temperature": 0.1,
            "max_tokens": 256
        }
        response = requests.post(f"{self.base_url}/v1/chat/completions", json=payload, headers=self.headers)
        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"]
