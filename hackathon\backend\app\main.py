from flask import Flask
from flask_cors import CORS
from flasgger import Swagger

from app.routes.nl2sql import nl2sql_bp
from app.routes.schema import schema_bp
from app.routes.charts import charts_bp
from app.routes.datasets import datasets_bp

app = Flask(__name__)
CORS(app)
Swagger(app)

app.register_blueprint(nl2sql_bp)
app.register_blueprint(schema_bp)
app.register_blueprint(charts_bp)
app.register_blueprint(datasets_bp)

if __name__ == "__main__":
    app.run(port=5000, debug=True)
