import React, { useState } from 'react';
import { Button, Input, Modal, Radio, Select, message } from 'antd';
import axios from 'axios';

const NL2SQLBar = ({
  onInsertSQL,
  ai_model,
  database,
  schema,
  table,
  database_id,
}) => {
  const [question, setQuestion] = useState('');
  const [loading, setLoading] = useState(false);
  const [lastGeneratedSQL, setLastGeneratedSQL] = useState('');
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportName, setReportName] = useState('');
  const [creatingReport, setCreatingReport] = useState(false);

  // Lưu lại loại chart được AI recommend
  const [recommendChartType, setRecommendChartType] = useState('');
  // Dashboard option
  const [dashboardOption, setDashboardOption] = useState('none');
  const [selectedDashboard, setSelectedDashboard] = useState(null);
  const [newDashboardName, setNewDashboardName] = useState('');

  // Dashboards state
  const [dashboards, setDashboards] = useState([]);
  const [loadingDashboards, setLoadingDashboards] = useState(false);

  // Load dashboards từ API khi cần
  const loadDashboards = async () => {
    if (dashboards.length > 0) return; // Đã load rồi thì không load lại

    setLoadingDashboards(true);
    try {
      const response = await axios.get(
        'http://localhost:5000/api/get_dashboards',
      );
      if (response.data.success) {
        const dashboardOptions = response.data.dashboards.map(
          (dashboard: any) => ({
            label: dashboard.title,
            value: dashboard.id,
          }),
        );
        setDashboards(dashboardOptions);
      } else {
        message.error('Không thể tải danh sách dashboard');
      }
    } catch (error) {
      message.error('Lỗi khi tải danh sách dashboard');
    } finally {
      setLoadingDashboards(false);
    }
  };

  // Gen SQL bằng NL2SQL API
  const handleGenerate = async () => {
    if (!question) return;
    setLoading(true);
    try {
      const res = await axios.post('http://localhost:5000/api/nl2sql', {
        question,
        ai_model,
        database,
        schema,
        table,
      });
      if (res.data?.sql) {
        setLastGeneratedSQL(res.data.sql);
        onInsertSQL(res.data.sql);
      } else {
        message.error('Không sinh được SQL, thử lại!');
      }
    } catch (e) {
      message.error('Lỗi gọi API NL2SQL');
    }
    setLoading(false);
  };

  // Khi bấm "Tạo báo cáo", lấy luôn loại chart từ recommend API
  const handleShowReportModal = async () => {
    if (!lastGeneratedSQL) return;
    setCreatingReport(true);
    try {
      // Gọi API recommend chart (lấy loại chart đề xuất)
      const recommendRes = await axios.post(
        'http://localhost:5000/api/recommend_chart',
        {
          sql: lastGeneratedSQL,
          ai_model,
        },
      );
      if (recommendRes.data?.success && recommendRes.data?.recommend) {
        setRecommendChartType(recommendRes.data.recommend.chart_type || '');
        setShowReportModal(true);
      } else {
        message.error('Không nhận được đề xuất chart phù hợp!');
      }
    } catch (e) {
      message.error('Lỗi recommend chart!');
    }
    setCreatingReport(false);
  };

  const handleCreateReport = async () => {
    if (!lastGeneratedSQL) return;
    setCreatingReport(true);
    try {
      // 1. Tạo dataset
      const datasetBody = {
        database,
        schema,
        sql: lastGeneratedSQL,
        table_name: reportName || `AutoDataset_${Date.now()}`,
        is_managed_externally: false,
        external_url: null,
      };
      const datasetRes = await axios.post(
        'http://localhost:5000/api/auto_create_dataset',
        datasetBody,
      );

      if (!datasetRes.data?.success || !datasetRes.data?.dataset?.id) {
        message.error(
          `Tạo dataset thất bại: ${datasetRes.data?.message || ''}`,
        );
        setCreatingReport(false);
        return;
      }

      const dataset_id = datasetRes.data.dataset.id;

      // 2. Recommend chart đã lấy ở trên, gán lại cho chắc chắn
      const recommendRes = await axios.post(
        'http://localhost:5000/api/recommend_chart',
        {
          sql: lastGeneratedSQL,
          ai_model,
        },
      );
      if (!recommendRes.data?.success || !recommendRes.data?.recommend) {
        message.error('Không nhận được đề xuất chart phù hợp!');
        setCreatingReport(false);
        return;
      }
      const { recommend } = recommendRes.data;

      // 3. Tạo chart (không gắn dashboard ở đây)
      const chartPayload = {
        ...recommend,
        viz_type: recommend.chart_type,
        chart_name: reportName || 'Auto Chart',
        dataset_id,
        row_limit: 10000,
        sql: lastGeneratedSQL,
      };

      const chartRes = await axios.post(
        'http://localhost:5000/api/create_chart',
        chartPayload,
      );

      if (!chartRes.data?.success || !chartRes.data?.chart_id) {
        message.error(`Tạo báo cáo thất bại: ${chartRes.data?.message || ''}`);
        setCreatingReport(false);
        return;
      }

      const { chart_id } = chartRes.data;
      const { chart_url } = chartRes.data;

      // 4. Xử lý dashboard logic
      if (dashboardOption === 'existing' && selectedDashboard) {
        // Gắn chart vào dashboard có sẵn
        const addToDashboardRes = await axios.post(
          'http://localhost:5000/api/add_chart_to_dashboard',
          {
            dashboard_id: selectedDashboard,
            chart_id,
            chart_name: reportName || 'Auto Chart',
          },
        );

        if (!addToDashboardRes.data?.success) {
          message.error(
            `Không thể thêm chart vào dashboard: ${addToDashboardRes.data?.message || ''}`,
          );
        } else {
          message.success('Đã tạo báo cáo và thêm vào dashboard thành công!');
        }
      } else if (dashboardOption === 'new' && newDashboardName) {
        // Tạo dashboard mới trước
        const createDashboardRes = await axios.post(
          'http://localhost:5000/api/create_dashboard',
          {
            dashboard_title: newDashboardName,
          },
        );

        if (
          !createDashboardRes.data?.success ||
          !createDashboardRes.data?.dashboard?.id
        ) {
          message.error(
            `Không thể tạo dashboard mới: ${createDashboardRes.data?.message || ''}`,
          );
        } else {
          // Sau đó gắn chart vào dashboard mới
          const new_dashboard_id = createDashboardRes.data.dashboard.id;
          const addToDashboardRes = await axios.post(
            'http://localhost:5000/api/add_chart_to_dashboard',
            {
              dashboard_id: new_dashboard_id,
              chart_id,
              chart_name: reportName || 'Auto Chart',
            },
          );

          if (!addToDashboardRes.data?.success) {
            message.error(
              `Không thể thêm chart vào dashboard mới: ${addToDashboardRes.data?.message || ''}`,
            );
          } else {
            message.success('Đã tạo báo cáo và dashboard mới thành công!');
          }
        }
      } else {
        // dashboardOption === 'none' - chỉ tạo chart độc lập
        message.success('Đã tạo báo cáo thành công!');
      }

      // Mở chart trong tab mới
      window.open(chart_url, '_blank');
      setShowReportModal(false);
      setReportName('');
    } catch (e) {
      message.error(
        `Lỗi khi tạo báo cáo: ${e.response?.data?.message || e.message}`,
      );
    }
    setCreatingReport(false);
  };

  // Function tương tự handleCreateReport nhưng redirect đến dashboard
  const handleCreateReportAndGoToDashboard = async () => {
    if (!lastGeneratedSQL) return;
    setCreatingReport(true);
    try {
      // 1. Tạo dataset
      const datasetBody = {
        database,
        schema,
        sql: lastGeneratedSQL,
        table_name: reportName || `AutoDataset_${Date.now()}`,
        is_managed_externally: false,
        external_url: null,
      };
      const datasetRes = await axios.post(
        'http://localhost:5000/api/auto_create_dataset',
        datasetBody,
      );

      if (!datasetRes.data?.success || !datasetRes.data?.dataset?.id) {
        message.error(
          `Tạo dataset thất bại: ${datasetRes.data?.message || ''}`,
        );
        setCreatingReport(false);
        return;
      }

      const dataset_id = datasetRes.data.dataset.id;

      // 2. Recommend chart
      const recommendRes = await axios.post(
        'http://localhost:5000/api/recommend_chart',
        {
          sql: lastGeneratedSQL,
          ai_model,
        },
      );
      if (!recommendRes.data?.success || !recommendRes.data?.recommend) {
        message.error('Không nhận được đề xuất chart phù hợp!');
        setCreatingReport(false);
        return;
      }
      const { recommend } = recommendRes.data;

      // 3. Tạo chart
      const chartPayload = {
        ...recommend,
        viz_type: recommend.chart_type,
        chart_name: reportName || 'Auto Chart',
        dataset_id,
        row_limit: 10000,
        sql: lastGeneratedSQL,
      };

      const chartRes = await axios.post(
        'http://localhost:5000/api/create_chart',
        chartPayload,
      );

      if (!chartRes.data?.success || !chartRes.data?.chart_id) {
        message.error(`Tạo báo cáo thất bại: ${chartRes.data?.message || ''}`);
        setCreatingReport(false);
        return;
      }

      const { chart_id } = chartRes.data;
      let dashboard_url = null;

      // 4. Xử lý dashboard logic và lấy dashboard URL
      if (dashboardOption === 'existing' && selectedDashboard) {
        const addToDashboardRes = await axios.post(
          'http://localhost:5000/api/add_chart_to_dashboard',
          {
            dashboard_id: selectedDashboard,
            chart_id,
            chart_name: reportName || 'Auto Chart',
          },
        );

        if (!addToDashboardRes.data?.success) {
          message.error(
            `Không thể thêm chart vào dashboard: ${addToDashboardRes.data?.message || ''}`,
          );
        } else {
          message.success('Đã tạo báo cáo và thêm vào dashboard thành công!');
          // Tạo dashboard URL từ selectedDashboard ID
          dashboard_url = `/superset/dashboard/${selectedDashboard}/`;
        }
      } else if (dashboardOption === 'new' && newDashboardName) {
        const createDashboardRes = await axios.post(
          'http://localhost:5000/api/create_dashboard',
          {
            dashboard_title: newDashboardName,
          },
        );

        if (
          !createDashboardRes.data?.success ||
          !createDashboardRes.data?.dashboard?.id
        ) {
          message.error(
            `Không thể tạo dashboard mới: ${createDashboardRes.data?.message || ''}`,
          );
        } else {
          const new_dashboard_id = createDashboardRes.data.dashboard.id;
          const addToDashboardRes = await axios.post(
            'http://localhost:5000/api/add_chart_to_dashboard',
            {
              dashboard_id: new_dashboard_id,
              chart_id,
              chart_name: reportName || 'Auto Chart',
            },
          );

          if (!addToDashboardRes.data?.success) {
            message.error(
              `Không thể thêm chart vào dashboard mới: ${addToDashboardRes.data?.message || ''}`,
            );
          } else {
            message.success('Đã tạo báo cáo và dashboard mới thành công!');
            // Sử dụng dashboard URL từ API response
            dashboard_url = createDashboardRes.data.dashboard.url;
          }
        }
      } else {
        message.warning(
          'Vui lòng chọn dashboard để có thể chuyển đến dashboard!',
        );
        setCreatingReport(false);
        return;
      }

      // 5. Redirect đến dashboard nếu có URL
      if (dashboard_url) {
        // Preserve native_filters_key nếu có trong URL hiện tại
        const currentParams = new URLSearchParams(window.location.search);
        const nativeFiltersKey = currentParams.get('native_filters_key');

        let finalDashboardUrl = dashboard_url;
        if (nativeFiltersKey) {
          const separator = dashboard_url.includes('?') ? '&' : '?';
          finalDashboardUrl += `${separator}native_filters_key=${nativeFiltersKey}`;
        }

        // Sử dụng window.location để navigate (tương tự SaveModal)
        window.location.href = finalDashboardUrl;
      }

      setShowReportModal(false);
      setReportName('');
    } catch (e) {
      message.error(
        `Lỗi khi tạo báo cáo: ${e.response?.data?.message || e.message}`,
      );
    }
    setCreatingReport(false);
  };

  return (
    <>
      <div style={{ display: 'flex', gap: 8, marginBottom: 8 }}>
        <Input
          placeholder="Nhập câu hỏi tự nhiên (ví dụ: Liệt kê các sản phẩm giá cao nhất)"
          value={question}
          onChange={e => setQuestion(e.target.value)}
          onPressEnter={handleGenerate}
          style={{ flex: 1 }}
        />
        <Button type="primary" loading={loading} onClick={handleGenerate}>
          Gen SQL
        </Button>
        <Button
          type="primary"
          disabled={!lastGeneratedSQL}
          loading={creatingReport}
          onClick={handleShowReportModal}
        >
          Tạo báo cáo
        </Button>
      </div>
      <Modal
        open={showReportModal}
        title="Tạo báo cáo/biểu đồ từ SQL"
        onCancel={() => setShowReportModal(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowReportModal(false)}>
            Hủy
          </Button>,
          <Button
            key="save-and-go"
            type="primary"
            loading={creatingReport}
            onClick={handleCreateReportAndGoToDashboard}
          >
            Save & go to dashboard
          </Button>,
          <Button
            key="save"
            type="primary"
            loading={creatingReport}
            onClick={handleCreateReport}
          >
            Tạo báo cáo
          </Button>,
        ]}
      >
        <div style={{ marginBottom: 12 }}>
          <label>SQL nguồn</label>
          <Input.TextArea value={lastGeneratedSQL} readOnly rows={4} />
        </div>
        <div style={{ marginBottom: 12 }}>
          <label>
            Loại biểu đồ được đề xuất:{' '}
            <b>
              {recommendChartType
                ? recommendChartType.toUpperCase()
                : '(Đang tải...)'}
            </b>
          </label>
          <div style={{ color: '#888', fontSize: 12 }}>
            (AI đã đề xuất dựa trên truy vấn SQL)
          </div>
        </div>
        <div style={{ marginBottom: 12 }}>
          <label>Tên biểu đồ</label>
          <Input
            value={reportName}
            onChange={e => setReportName(e.target.value)}
            placeholder="Nhập tên báo cáo (tùy chọn)"
          />
        </div>
        <div style={{ marginBottom: 0 }}>
          <label>Lưu vào dashboard:</label>
          <Radio.Group
            value={dashboardOption}
            onChange={e => {
              const { value } = e.target;
              setDashboardOption(value);
              // Gọi API load dashboards khi chọn "existing"
              if (value === 'existing') {
                loadDashboards();
              }
            }}
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 6,
              marginTop: 6,
            }}
          >
            <Radio value="existing">
              Gắn vào dashboard đã có &nbsp;
              <Select
                style={{ width: 200 }}
                placeholder={
                  loadingDashboards ? 'Đang tải...' : 'Chọn dashboard'
                }
                value={selectedDashboard}
                onChange={setSelectedDashboard}
                disabled={dashboardOption !== 'existing' || loadingDashboards}
                options={dashboards}
                loading={loadingDashboards}
                allowClear
              />
            </Radio>
            <Radio value="new">
              Tạo dashboard mới &nbsp;
              <Input
                style={{ width: 180 }}
                placeholder="Nhập tên dashboard mới"
                value={newDashboardName}
                onChange={e => setNewDashboardName(e.target.value)}
                disabled={dashboardOption !== 'new'}
              />
            </Radio>
            <Radio value="none">Không gắn vào dashboard</Radio>
          </Radio.Group>
        </div>
      </Modal>
    </>
  );
};

export default NL2SQLBar;
