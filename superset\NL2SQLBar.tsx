import React, { useState } from 'react';
import { Button, Input, Modal, Radio, Select, message } from 'antd';
import axios from 'axios';

const dashboards = [
  { label: "Sales Dashboard", value: "sales" },
  { label: "Product Dashboard", value: "product" },
  // TODO: Load thực tế từ API hoặc props
];

const NL2SQLBar = ({
  onInsertSQL,
  ai_model,
  database,
  schema,
  table,
  database_id,
}) => {
  const [question, setQuestion] = useState('');
  const [loading, setLoading] = useState(false);
  const [lastGeneratedSQL, setLastGeneratedSQL] = useState('');
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportName, setReportName] = useState('');
  const [creatingReport, setCreatingReport] = useState(false);

  // Lưu lại loại chart được AI recommend
  const [recommendChartType, setRecommendChartType] = useState('');
  // Dashboard option
  const [dashboardOption, setDashboardOption] = useState('none');
  const [selectedDashboard, setSelectedDashboard] = useState(null);
  const [newDashboardName, setNewDashboardName] = useState('');

  // Gen SQL bằng NL2SQL API
  const handleGenerate = async () => {
    if (!question) return;
    setLoading(true);
    try {
      const res = await axios.post('http://localhost:5000/api/nl2sql', {
        question,
        ai_model,
        database,
        schema,
        table,
      });
      if (res.data?.sql) {
        setLastGeneratedSQL(res.data.sql);
        onInsertSQL(res.data.sql);
      } else {
        message.error('Không sinh được SQL, thử lại!');
      }
    } catch (e) {
      message.error('Lỗi gọi API NL2SQL');
    }
    setLoading(false);
  };

  // Khi bấm "Tạo báo cáo", lấy luôn loại chart từ recommend API
  const handleShowReportModal = async () => {
    if (!lastGeneratedSQL) return;
    setCreatingReport(true);
    try {
      // Gọi API recommend chart (lấy loại chart đề xuất)
      const recommendRes = await axios.post('http://localhost:5000/api/recommend_chart', {
        sql: lastGeneratedSQL,
        ai_model: ai_model,
      });
      if (recommendRes.data?.success && recommendRes.data?.recommend) {
        setRecommendChartType(recommendRes.data.recommend.chart_type || '');
        setShowReportModal(true);
      } else {
        message.error('Không nhận được đề xuất chart phù hợp!');
      }
    } catch (e) {
      message.error('Lỗi recommend chart!');
    }
    setCreatingReport(false);
  };

  const handleCreateReport = async () => {
    if (!lastGeneratedSQL) return;
    setCreatingReport(true);
    try {
      // 1. Tạo dataset
      const datasetBody = {
        database,
        schema,
        sql: lastGeneratedSQL,
        table_name: reportName || 'AutoDataset_' + Date.now(),
        is_managed_externally: false,
        external_url: null,
      };
      const datasetRes = await axios.post('http://localhost:5000/api/auto_create_dataset', datasetBody);

      if (!datasetRes.data?.success || !datasetRes.data?.dataset?.id) {
        message.error('Tạo dataset thất bại: ' + (datasetRes.data?.message || ''));
        setCreatingReport(false);
        return;
      }

      const dataset_id = datasetRes.data.dataset.id;

      // 2. Recommend chart đã lấy ở trên, gán lại cho chắc chắn
      const recommendRes = await axios.post('http://localhost:5000/api/recommend_chart', {
        sql: lastGeneratedSQL,
        ai_model: ai_model,
      });
      if (!recommendRes.data?.success || !recommendRes.data?.recommend) {
        message.error('Không nhận được đề xuất chart phù hợp!');
        setCreatingReport(false);
        return;
      }
      const recommend = recommendRes.data.recommend;

      // 3. Tạo chart (merge thông tin dashboard nếu có)
      const chartPayload = {
        ...recommend,
        viz_type: recommend.chart_type,
        chart_name: reportName || 'Auto Chart',
        dataset_id,
        row_limit: 10000,
        sql: lastGeneratedSQL,
      };

      // Gắn dashboard (nếu chọn)
      if (dashboardOption === 'existing' && selectedDashboard) {
        chartPayload.dashboard_id = selectedDashboard;
      }
      if (dashboardOption === 'new' && newDashboardName) {
        chartPayload.new_dashboard_name = newDashboardName;
      }
      // Nếu "none", không cần bổ sung

      const chartRes = await axios.post('http://localhost:5000/api/create_chart', chartPayload);

      if (chartRes.data?.success && chartRes.data?.chart_url) {
        window.open(chartRes.data.chart_url, '_blank');
        setShowReportModal(false);
        setReportName('');
        message.success('Đã tạo báo cáo thành công!');
      } else {
        message.error('Tạo báo cáo thất bại: ' + (chartRes.data?.message || ''));
      }
    } catch (e) {
      message.error('Lỗi khi tạo báo cáo: ' + (e.response?.data?.message || e.message));
    }
    setCreatingReport(false);
  };

  return (
    <>
      <div style={{ display: 'flex', gap: 8, marginBottom: 8 }}>
        <Input
          placeholder="Nhập câu hỏi tự nhiên (ví dụ: Liệt kê các sản phẩm giá cao nhất)"
          value={question}
          onChange={e => setQuestion(e.target.value)}
          onPressEnter={handleGenerate}
          style={{ flex: 1 }}
        />
        <Button type="primary" loading={loading} onClick={handleGenerate}>
          Gen SQL
        </Button>
        <Button
          type="primary"
          disabled={!lastGeneratedSQL}
          loading={creatingReport}
          onClick={handleShowReportModal}
        >
          Tạo báo cáo
        </Button>
      </div>
      <Modal
        open={showReportModal}
        title="Tạo báo cáo/biểu đồ từ SQL"
        onCancel={() => setShowReportModal(false)}
        onOk={handleCreateReport}
        okText="Tạo báo cáo"
        cancelText="Hủy"
        confirmLoading={creatingReport}
      >
        <div style={{ marginBottom: 12 }}>
          <label>SQL nguồn</label>
          <Input.TextArea value={lastGeneratedSQL} readOnly rows={4} />
        </div>
        <div style={{ marginBottom: 12 }}>
          <label>
            Loại biểu đồ được đề xuất: <b>{recommendChartType ? recommendChartType.toUpperCase() : '(Đang tải...)'}</b>
          </label>
          <div style={{ color: '#888', fontSize: 12 }}>
            (AI đã đề xuất dựa trên truy vấn SQL)
          </div>
        </div>
        <div style={{ marginBottom: 12 }}>
          <label>Tên biểu đồ</label>
          <Input
            value={reportName}
            onChange={e => setReportName(e.target.value)}
            placeholder="Nhập tên báo cáo (tùy chọn)"
          />
        </div>
        <div style={{ marginBottom: 0 }}>
          <label>Lưu vào dashboard:</label>
          <Radio.Group
            value={dashboardOption}
            onChange={e => setDashboardOption(e.target.value)}
            style={{ display: 'flex', flexDirection: 'column', gap: 6, marginTop: 6 }}
          >
            <Radio value="existing">
              Gắn vào dashboard đã có &nbsp;
              <Select
                style={{ width: 200 }}
                placeholder="Chọn dashboard"
                value={selectedDashboard}
                onChange={setSelectedDashboard}
                disabled={dashboardOption !== 'existing'}
                options={dashboards}
                allowClear
              />
            </Radio>
            <Radio value="new">
              Tạo dashboard mới &nbsp;
              <Input
                style={{ width: 180 }}
                placeholder="Nhập tên dashboard mới"
                value={newDashboardName}
                onChange={e => setNewDashboardName(e.target.value)}
                disabled={dashboardOption !== 'new'}
              />
            </Radio>
            <Radio value="none">Không gắn vào dashboard</Radio>
          </Radio.Group>
        </div>
      </Modal>
    </>
  );
};

export default NL2SQLBar;
