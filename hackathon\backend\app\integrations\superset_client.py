from app.config import settings
import requests
import os
import json
from app.utils.sql_utils import read_token_from_file
from app.utils.superset_token_manager import get_superset_token

class SupersetClient:
    def __init__(self):
        self.base_url = os.getenv("SUPERSET_FE_URL")
        self.token = get_superset_token()
        self.headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}",
            "Origin": self.base_url,
            "Referer": f"{self.base_url}/sqllab/",
        }

    def create_dataset(self, payload):
        self.token = get_superset_token()
        self.headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}",
            "Origin": self.base_url,
            "Referer": f"{self.base_url}/sqllab/",
        }
        url = f"{self.base_url}/api/v1/dataset/"
        print("[DEBUG] Payload gửi lên Superset:", payload)
        resp = requests.post(url, json=payload, headers=self.headers)
        print("[DEBUG] Superset status code:", resp.status_code)
        print("[DEBUG] Superset response:", resp.text)
        try:
            return resp.json(), resp.status_code
        except Exception:
            return resp.text, resp.status_code

    def create_chart(self, chart_type, data):
        self.token = get_superset_token()
        self.headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}",
            "Origin": self.base_url,
            "Referer": f"{self.base_url}/sqllab/",
        }        
        url = f"{self.base_url}/api/v1/chart/"
        # Đảm bảo params là string (JSON)
        params = data.get("params", "{}")
        if not isinstance(params, str):
            params = json.dumps(params)
        payload = {
            "slice_name": data.get("chart_name", "Auto Chart"),
            "viz_type": chart_type,
            "datasource_id": data.get("dataset_id"),
            "datasource_type": "table",
            "params": params
        }
        print("[DEBUG] Payload gửi lên Superset Chart:", payload)
        resp = requests.post(url, json=payload, headers=self.headers)
        print("[DEBUG] Superset Chart status code:", resp.status_code)
        print("[DEBUG] Superset Chart response:", resp.text)
        try:
            return resp.json(), resp.status_code
        except Exception:
            return resp.text, resp.status_code
