from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import os
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_community.utilities import SQLDatabase
from langchain_openai import ChatOpenAI
from langchain_core.runnables import RunnablePassthrough
import re
from sqlalchemy import text, create_engine

# Load env
load_dotenv()
FPT_MODEL = os.getenv("FPT_MODEL", "fpt-4-32k")
FPT_API_KEY = os.getenv("FPT_API_KEY")
FPT_API_BASE = os.getenv("FPT_API_BASE")

def init_database(user: str, password: str, host: str, port: str, database: str) -> SQLDatabase:
    db_uri = f"postgresql://{user}:{password}@{host}:{port}/{database}"
    return SQLDatabase.from_uri(db_uri)

def clean_response(text: str) -> str:
    text = re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL | re.IGNORECASE)
    text = re.sub(r"```.*?```", "", text, flags=re.DOTALL)
    return text.strip()

def cache_schema_per_table(db: SQLDatabase, db_name: str):
    table_ddl_text = db.get_table_info()
    engine = db._engine
    with engine.connect() as conn:
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS table_schemas (
                db_name VARCHAR,
                table_name VARCHAR,
                ddl VARCHAR
            )
        """))
        table_blocks = re.findall(r"(CREATE TABLE .*?\);)", table_ddl_text, re.DOTALL)
        for block in table_blocks:
            match = re.search(r"CREATE TABLE (\w+)", block)
            if not match:
                continue
            table_name = match.group(1)
            update_result = conn.execute(
                text("""
                    UPDATE table_schemas SET ddl = :ddl
                    WHERE db_name = :db_name AND table_name = :table_name
                """),
                {"db_name": db_name, "table_name": table_name, "ddl": block.strip()}
            )
            if update_result.rowcount == 0:
                conn.execute(
                    text("""
                        INSERT INTO table_schemas (db_name, table_name, ddl)
                        VALUES (:db_name, :table_name, :ddl)
                    """),
                    {"db_name": db_name, "table_name": table_name, "ddl": block.strip()}
                )

def get_cached_schema(db: SQLDatabase, db_name: str) -> str:
    try:
        with db._engine.connect() as conn:
            result = conn.execute(text("SELECT ddl FROM table_schemas WHERE db_name = :db"), {"db": db_name})
            rows = result.fetchall()
            return "\n\n".join(row[0] for row in rows) if rows else ""
    except Exception as e:
        return f"❌ Schema load error: {e}"

def fpt_complete(messages: list) -> str:
    llm = ChatOpenAI(
        model=FPT_MODEL,
        openai_api_key=FPT_API_KEY,
        openai_api_base=FPT_API_BASE,
        temperature=0.0,
    )
    response = llm.invoke(messages)
    return response.content.strip()

def extract_sql_only(response: str) -> str:
    response = re.sub(r"<think>.*?</think>", "", response, flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r"```.*?```", "", response, flags=re.DOTALL)
    lines = [line.strip().rstrip(';') for line in response.splitlines() if line.strip()]
    candidates = [line for line in lines if line.lower().startswith("select") and " from " in line.lower() and len(line) > 10]
    if candidates:
        return candidates[-1]
    select_lines = [line for line in lines if line.lower().startswith("select")]
    if select_lines:
        return max(select_lines, key=len)
    m = re.search(r"(SELECT\s+.+FROM\s+.+?;?)", response, re.IGNORECASE | re.DOTALL)
    if m:
        return m.group(1).strip().rstrip(';')
    return response.strip()

def get_sql_chain(db, db_name):
    def chain_fn(vars):
        cache_schema_per_table(db, db_name)
        schema = get_cached_schema(db, db_name)
        chat_history = vars["chat_history"]
        user_query = vars["question"]
        messages = []
        sys_prompt = (
            "You are a SQL expert. Based on the schema below, output ONLY a valid SQL SELECT statement that answers the user's question. "
            "Do NOT explain, do NOT add markdown, do NOT add comments. Only the SQL, one line.\n"
            "- Always use the exact column and table names from the schema (e.g., use products.name not product_name).\n"
            "- If there are relationships via columns like customer_id, product_id, order_id, always use JOIN statements based on these keys.\n"
            "- Use JOIN ... ON ... syntax, not implicit join.\n"
            "For example, to get all products for a customer: "
            "SELECT p.name FROM customers c JOIN orders o ON c.id = o.customer_id "
            "JOIN order_details od ON o.id = od.order_id JOIN products p ON od.product_id = p.id WHERE c.name = 'Alice';\n"
            f"<SCHEMA>{schema}</SCHEMA>"
        )
        messages.append(SystemMessage(content=sys_prompt))
        for msg in chat_history:
            if isinstance(msg, HumanMessage):
                messages.append(HumanMessage(content=msg.content))
            elif isinstance(msg, AIMessage):
                messages.append(AIMessage(content=msg.content))
        messages.append(HumanMessage(content=user_query))
        response = fpt_complete(messages)
        print("\n==== RAW LLM RESPONSE ====\n", response)
        sql = extract_sql_only(response)
        print("\n==== EXTRACTED SQL ====\n", sql)
        if len(sql) < 15 or " from " not in sql.lower():
            raise Exception(f"❌ SQL generate error: Model did not return a valid SQL statement: {sql}")
        return sql
    return RunnablePassthrough.assign(query=chain_fn)

# ---- FLASK API for Superset ----
app = Flask(__name__)
CORS(app)

@app.route('/api/nl2sql', methods=['POST'])
def nl2sql_api():
    data = request.json
    question = data.get('question', '')
    database = data.get('database', '')       # e.g. postgresql
    schema = data.get('schema', '')           # e.g. public
    table = data.get('table', '')             # e.g. birth_names

    # Bạn cần tự mapping db info (user/pass/host/port/catalog/schema) theo config thực tế!
    # Ví dụ, nếu dùng 1 Trino connection mặc định:
    POSTGRES_USER = os.getenv("POSTGRES_USER", "examples")
    POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", "examples")
    POSTGRES_HOST = os.getenv("POSTGRES_HOST", "db")
    POSTGRES_PORT = os.getenv("POSTGRES_PORT", "5432")
    POSTGRES_DB =  os.getenv("POSTGRES_DB", "examples") or schema

    db = init_database(
        POSTGRES_USER,
        POSTGRES_PASSWORD,
        POSTGRES_HOST,
        POSTGRES_PORT,
        POSTGRES_DB
    )

    db_name = POSTGRES_DB
    chat_history = []  # Nếu muốn giữ history thì bổ sung logic cho từng phiên (user)
    try:
        sql_chain = get_sql_chain(db, db_name)
        sql = sql_chain.invoke({
            "question": question,
            "chat_history": chat_history,
        })["query"]
        print("[NL2SQL] Question:", question)
        print("[NL2SQL] SQL:", sql)
    except Exception as e:
        sql = f"-- ERROR: {e}"
        print("[NL2SQL] ERROR:", e)

    return jsonify({'sql': sql})

if __name__ == '__main__':
    app.run(port=5000, debug=True)
