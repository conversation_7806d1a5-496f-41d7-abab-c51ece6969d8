from sqlalchemy import text
from app.repositories.trino_repo import get_trino_db
from app.config import settings

# def cache_schema():
#     db = get_trino_db()
#     engine = db.engine
#     with engine.connect() as conn:
#         conn.execute(text("""
#             CREATE TABLE IF NOT EXISTS table_schemas (
#                 db_name VARCHAR,
#                 table_name VARCHAR,
#                 ddl VARCHAR
#             )
#         """))
#         tables = [row[0] for row in conn.execute(text("SHOW TABLES"))]
#         for table in tables:
#             ddl_row = conn.execute(text(f"SHOW CREATE TABLE {table}")).fetchone()
#             ddl = ddl_row[0].strip()
#             db_name = settings.TRINO_SCHEMA

#             # Kiểm tra xem đã tồn tại schema với db_name, table_name chưa
#             existed = conn.execute(
#                 text("""
#                     SELECT COUNT(*) FROM table_schemas
#                     WHERE db_name = :db AND table_name = :table
#                 """),
#                 {"db": db_name, "table": table}
#             ).scalar()

#             if existed:
#                 # Nếu đã tồn tại, update ddl nếu khác
#                 conn.execute(
#                     text("""
#                         UPDATE table_schemas
#                         SET ddl = :ddl
#                         WHERE db_name = :db AND table_name = :table
#                     """),
#                     {"db": db_name, "table": table, "ddl": ddl}
#                 )
#             else:
#                 # Nếu chưa có, insert mới
#                 conn.execute(
#                     text("""
#                         INSERT INTO table_schemas (db_name, table_name, ddl)
#                         VALUES (:db, :table, :ddl)
#                     """),
#                     {"db": db_name, "table": table, "ddl": ddl}
#                 )

def cache_schema():
    db = get_trino_db()
    engine = db.engine
    with engine.connect() as conn:
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS table_schemas (
                db_name VARCHAR,
                table_name VARCHAR,
                ddl VARCHAR
            )
        """))
        db_name = settings.TRINO_SCHEMA

        # Lấy danh sách bảng thực tế
        tables_actual = set([row[0] for row in conn.execute(text("SHOW TABLES"))])

        # Lấy danh sách bảng đang lưu cache
        tables_cached = set([
            row[0] for row in conn.execute(
                text("SELECT table_name FROM table_schemas WHERE db_name = :db"),
                {"db": db_name}
            )
        ])

        # Xác định các bảng đã bị xóa (chỉ còn trong cache)
        deleted_tables = tables_cached - tables_actual

        # Xóa các dòng schema đã obsolete
        for table in deleted_tables:
            conn.execute(
                text("DELETE FROM table_schemas WHERE db_name = :db AND table_name = :table"),
                {"db": db_name, "table": table}
            )

        # Tiếp tục refresh/update schema như logic upsert đã gửi trước
        for table in tables_actual:
            ddl_row = conn.execute(text(f"SHOW CREATE TABLE {table}")).fetchone()
            ddl = ddl_row[0].strip()

            existed = conn.execute(
                text("""
                    SELECT COUNT(*) FROM table_schemas
                    WHERE db_name = :db AND table_name = :table
                """),
                {"db": db_name, "table": table}
            ).scalar()

            if existed:
                conn.execute(
                    text("""
                        UPDATE table_schemas
                        SET ddl = :ddl
                        WHERE db_name = :db AND table_name = :table
                    """),
                    {"db": db_name, "table": table, "ddl": ddl}
                )
            else:
                conn.execute(
                    text("""
                        INSERT INTO table_schemas (db_name, table_name, ddl)
                        VALUES (:db, :table, :ddl)
                    """),
                    {"db": db_name, "table": table, "ddl": ddl}
                )
