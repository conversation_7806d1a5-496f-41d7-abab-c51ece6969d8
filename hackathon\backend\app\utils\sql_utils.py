import os
import requests
import re

def extract_sql_only(response):
    response = re.sub(r"<think>.*?</think>", "", response, flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r"```.*?```", "", response, flags=re.DOTALL)
    lines = [line.strip().rstrip(';') for line in response.splitlines() if line.strip()]
    candidates = [line for line in lines if line.lower().startswith("select") and " from " in line.lower()]
    return candidates[-1] if candidates else response.strip()

def read_token_from_file(key: str, filename: str = "superset_tokens.txt") -> str:
    if not os.path.exists(filename):
        raise FileNotFoundError(f"File {filename} không tồn tại")
    with open(filename, "r") as f:
        for line in f:
            if line.startswith(f"{key}="):
                return line.strip().split("=", 1)[1]
    raise KeyError(f"Không tìm thấy {key} trong file {filename}")

def get_dataset_columns(datasource_id: int):
    # Đọc token (ưu tiên file, có thể đổi lại nếu lấy từ env)
    token = read_token_from_file("SUPERSET_ACCESS_TOKEN")
    fe_url = os.getenv("SUPERSET_FE_URL", "http://localhost:9000")
    headers = {"Authorization": f"Bearer {token}"}
    url = f"{fe_url}/api/v1/dataset/{datasource_id}"
    r = requests.get(url, headers=headers)
    if r.status_code != 200:
        raise Exception(f"Failed to fetch dataset info: {r.text}")
    data = r.json()
    return data.get("result", {}).get("columns", [])

def get_metric_column_id(datasource_id: int, column_name: str) -> int:
    columns = get_dataset_columns(datasource_id)
    target_col = column_name.strip() if column_name else None

    # Normalize tên cột: bỏ dấu ngoặc, lấy tên thật nếu là SUM(xxx) hoặc AVG(xxx), bỏ dấu ')' ở cuối.
    if target_col and target_col.endswith(')'):
        target_col = target_col[:-1]
    if target_col and "(" in target_col and ")" in target_col:
        match = re.search(r"\((.*?)\)", target_col)
        if match:
            target_col = match.group(1).strip()

    for col in columns:
        if col.get("column_name") == target_col:
            return col.get("id")

    print(f"[DEBUG] Columns in dataset {datasource_id}:", [col.get("column_name") for col in columns])
    raise Exception(f"Column '{column_name}' (normalized as '{target_col}') not found in dataset {datasource_id}")

def extract_metric_alias_from_sql(sql: str):
    """
    Trích xuất alias metric từ câu SQL ví dụ: SELECT ... SUM(x) AS total_x ...
    """
    m = re.search(r'\bAS\s+(\w+)', sql, re.IGNORECASE)
    return m.group(1) if m else None
