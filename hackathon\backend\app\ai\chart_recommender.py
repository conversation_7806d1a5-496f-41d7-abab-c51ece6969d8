from app.ai.fpt_llm import fpt_complete
import json
import re

def recommend_chart_from_ai(sql, schema_hint=None, ai_model=None):
    prompt = f"""
    Khi sinh JSON cấu hình chart, TUYỆT ĐỐI KHÔNG được sử dụng "columns": ["*"] hay "SELECT *".
    Luôn liệt kê đầy đủ tất cả các tên trường cụ thể (column name) của bảng.
    Ví dụ đúng: "columns": ["id", "name", "email"]
    Ví dụ sai: "columns": ["*"]

    Nếu SQL chứa SELECT *, hãy tra cứu schema để lấy tên tất cả các trường và liệt kê hết vào "columns".

    Hãy phân tích truy vấn SQL dưới đây và trả về loại biểu đồ Superset phù hợp nhất cùng các tham số cấu hình chính cho chart đó dưới dạng object JSON.
    CHỈ trả về JSON, không giải thích gì thêm.

    *** QUAN TRỌNG ***
    - Nếu SQL có nhiều cột trong mệnh đề GROUP BY, hãy xác định cột thời gian (ví dụ: year, date, created_at, order_year...) làm "x_axis" nếu có, các cột còn lại đưa vào "groupby".
    - Nếu không có cột thời gian, lấy cột đầu tiên trong GROUP BY làm "x_axis", các cột còn lại là "groupby".
    - Luôn trả về đầy đủ cả "x_axis" và "groupby" trong cấu hình trả về cho biểu đồ cột/thanh.
    - Trường "metrics" là danh sách các trường số được tính toán (ví dụ: SUM, COUNT, total_quantity,...).
    - Trường "columns" là danh sách đầy đủ tất cả các trường có trong kết quả SQL.

    Nếu là biểu đồ dạng bảng, trả về:
    {{"chart_type": "table", "columns": ["col1", "col2", ...]}}

    Nếu là biểu đồ cột/thanh (bar chart), trả về:
    {{"chart_type": "echarts_timeseries_bar", "x_axis": "col_x", "metrics": ["col_y"], "groupby": [...], "columns": ["col1", "col2", ...]}}

    Nếu là biểu đồ tròn (pie chart), trả về:
    {{"chart_type": "pie", "columns": ["col1"], "metrics": ["col_y"]}}

    Ví dụ:
    SQL: SELECT country, COUNT(*) FROM sales GROUP BY country
    JSON: {{"chart_type": "pie", "columns": ["country"], "metrics": ["COUNT(*)"]}}

    SQL: SELECT created_at, SUM(amount) FROM revenue GROUP BY created_at
    JSON: {{"chart_type": "echarts_timeseries_bar", "x_axis": "created_at", "metrics": ["SUM(amount)"], "groupby": [], "columns": ["created_at", "SUM(amount)"]}}

    SQL: SELECT name, EXTRACT(YEAR FROM order_date) AS order_year, SUM(quantity) AS total_quantity FROM customers GROUP BY name, order_year
    JSON: {{"chart_type": "echarts_timeseries_bar", "x_axis": "order_year", "metrics": ["total_quantity"], "groupby": ["name"], "columns": ["name", "order_year", "total_quantity"]}}

    SQL: SELECT * FROM users
    JSON: {{"chart_type": "table", "columns": ["id", "name", "email", ...]}}  # <-- Ghi rõ các trường

    Dưới đây là SQL cần phân tích:
    SQL: {sql}
    """
    
    messages = [{"role": "user", "content": prompt}]
    response = fpt_complete(messages, ai_model=ai_model)

    print("[DEBUG] AI response raw:", response)
    if not response or not isinstance(response, str) or not response.strip():
        return {
            "success": False,
            "message": "AI model không trả về kết quả hoặc kết quả rỗng.",
            "raw_response": response
        }

    # Loại bỏ ```json ... ```
    response_clean = response.strip()
    # Xóa mọi ``` hoặc ```json đầu/cuối chuỗi
    response_clean = re.sub(r"^```json\s*", "", response_clean)
    response_clean = re.sub(r"^```\s*", "", response_clean)
    response_clean = re.sub(r"\s*```$", "", response_clean)
    # Nếu có tiền tố "JSON:" cũng remove luôn cho chắc
    if response_clean.lower().startswith("json:"):
        response_clean = response_clean[5:].strip()

    try:
        return json.loads(response_clean)
    except Exception as e:
        print("[ERROR] Lỗi parse JSON:", e, "| Raw response:", response_clean)
        return {
            "success": False,
            "message": f"Lỗi parse JSON: {e}",
            "raw_response": response_clean
        }
