from sqlalchemy import create_engine, text
from app.config import settings

def get_trino_engine():
    uri = f"trino://{settings.TRINO_USER}:{settings.TRINO_PASSWORD}@{settings.TRINO_HOST}:{settings.TRINO_PORT}/{settings.TRINO_CATALOG}/{settings.TRINO_SCHEMA}"
    return create_engine(uri)

class TrinoDB:
    def __init__(self):
        self.engine = get_trino_engine()

    def get_cached_schema(self):
        with self.engine.connect() as conn:
            result = conn.execute(text("SELECT ddl FROM table_schemas"))
            return "\n\n".join(row[0] for row in result.fetchall())

def get_trino_db():
    return TrinoDB()
