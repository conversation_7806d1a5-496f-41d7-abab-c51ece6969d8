import requests
import time
import os

# <PERSON><PERSON><PERSON> biến cấu hình
SUPERSET_API_URL = "http://localhost:8088/api/v1/security/login"
USERNAME = "admin"
PASSWORD = "admin"
TOKEN_FILE = "superset_tokens.txt"  # File đích để ghi token

def refresh_token():
    payload = {
        "username": USERNAME,
        "password": PASSWORD,
        "provider": "db",
        "refresh": True
    }
    try:
        resp = requests.post(SUPERSET_API_URL, json=payload)
        resp.raise_for_status()
        data = resp.json()
        access_token = data.get("access_token")
        refresh_token = data.get("refresh_token")

        print("Access token:", access_token[:30], "...")  # In rút gọn

        # Ghi token vào file
        with open(TOKEN_FILE, "w") as f:
            f.write(f"SUPERSET_ACCESS_TOKEN={access_token}\n")
            f.write(f"SUPERSET_REFRESH_TOKEN={refresh_token}\n")

        print(f"Đã ghi token vào {TOKEN_FILE}")
    except Exception as e:
        print("ERROR:", str(e))

if __name__ == "__main__":
    while True:
        refresh_token()
        print("Chờ 10 phút trước lần làm mới tiếp theo...")
        time.sleep(10 * 60)
