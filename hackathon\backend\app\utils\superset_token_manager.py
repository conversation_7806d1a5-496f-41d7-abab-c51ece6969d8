import requests
import time
import threading
import os

class SupersetTokenManager:
    _instance = None
    _lock = threading.Lock()

    SUPERSET_API_URL = os.getenv("SUPERSET_API_URL")
    USERNAME = os.getenv("SUPERSET_USERNAME", "admin")
    PASSWORD = os.getenv("SUPERSET_PASSWORD", "admin")

    def __new__(cls):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
                    cls._instance.token = None
                    cls._instance.refresh_token = None
                    cls._instance.expire_time = 0
        return cls._instance

    def get_token(self):
        # Nếu còn hiệu lực trên 2 phút thì dùng lại token cũ
        if self.token and (self.expire_time - time.time() > 120):
            return self.token
        self.refresh_token_method()
        return self.token

    def refresh_token_method(self):
        payload = {
            "username": self.USERNAME,
            "password": self.PASSWORD,
            "provider": "db",
            "refresh": True
        }
        try:
            resp = requests.post(self.SUPERSET_API_URL, json=payload)
            resp.raise_for_status()
            data = resp.json()
            access_token = data.get("access_token")
            refresh_token = data.get("refresh_token")
            # Hầu hết Superset mặc định 15 phút, nhưng check nếu API có trả expires_in thì lấy luôn
            expires_in = data.get("expires_in", 900)
            self.token = access_token
            self.refresh_token = refresh_token
            self.expire_time = time.time() + expires_in
            print(f"[INFO] Superset token refreshed. Expires in {expires_in} seconds.")
        except Exception as e:
            print("[ERROR] Không lấy được token Superset:", str(e))
            raise

# --- Cách dùng trong backend/service ---
def get_superset_token():
    return SupersetTokenManager().get_token()

# Ví dụ trong integration:
# headers = {"Authorization": f"Bearer {get_superset_token()}"}
