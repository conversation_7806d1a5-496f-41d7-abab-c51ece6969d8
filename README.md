# 🚀 Natural Language to SQL and Report

Tích hợp FPT AI Model vào Apache Superset giúp chuyển đổi ngôn ngữ tự nhiên (NL) thành SQL, đồng thời hỗ trợ tự động tạo báo cáo dữ liệu thông minh.

---

## 🎯 Mục đích

- **Chuyển đổi ngôn ngữ tự nhiên (Natural Language) sang SQL** qua AI Model của FPT.
- **Tự động tạo báo cáo** dựa trên yêu cầu của người dùng, rút ngắn thời gian phân tích dữ liệu.
- **Tích hợp trực tiếp vào Apache Superset** với giao diện quen thuộc.

---

## 🏗️ Kiến trúc project

Project gồm **2 thành phần** chính:

1. **Backend**  
   - API server trung gian, kết nối FPT AI Model.
   - Xử lý truy vấn NL2SQL từ Frontend.
2. **Frontend**  
   - T<PERSON>y biến trên Apache Superset.
   - Thêm UI bar để nhập ngôn ngữ tự nhiên và tự động hóa tạo báo cáo.

---

## ⚙️ Hướng dẫn cài đặt

### 1. Cài đặt Backend

```bash
# Tạo môi trường ảo
python -m venv venv

# Kích hoạt môi trường ảo
source venv/bin/activate   # Trên Linux/Mac
venv\Scripts\activate      # Trên Windows

# Cài đặt thư viện phụ thuộc
pip install -r requirements.txt

# Chạy backend
python nl2sql_report.py
```

### 2. Cài đặt và tích hợp Frontend (Superset)

#### a. Clone mã nguồn Superset

```bash
git clone https://github.com/apache/superset.git
cd superset
```

#### b. Copy file `superset/superset_config.py` vào:

  ```
  /superset
  ```

#### c. Sửa file docker-compose.yml phần volume của superset_app thành như sau:

  ```
    volumes:
      - ./docker:/app/docker
      - ./superset:/app/superset
      - ./superset-frontend:/app/superset-frontend
      - superset_home:/app/superset_home
      - ./tests:/app/tests
      - ./superset_config.py:/app/pythonpath/superset_config.py
  ```

#### d. Khởi động Superset bằng Docker

```bash
docker-compose up -d
```

#### e. Khởi tạo database và tài khoản admin

```bash
docker exec -it superset_app bash

# Trong container:
superset db upgrade
superset fab create-admin
```

#### f. Copy các file UI tuỳ biến

- Copy file `superset/NL2SQLBar.tsx` vào:
  ```
  superset/superset-frontend/src/SqlLab/components
  ```
- Copy file `superset/index.tsx` vào:
  ```
  superset/superset-frontend/src/SqlLab/components/SqlEditor
  ```

#### g. Khởi động Frontend dev-server

```bash
cd superset-frontend
npm install
npm run dev-server
```

#### h. Truy cập giao diện

- Giao diện Frontend: [http://localhost:9000](http://localhost:9000)
- Đăng nhập bằng tài khoản admin vừa khởi tạo.

#### i. Exten superset token expire time

- Truy cập vào venv của backend, chạy lệnh python refresg_superset_token.py hệ thống sẽ tự động lấy token mỗi 15m và lưu vào file
- Khi gọi API tạo chart hay dashboard hệ thống sẽ lấy superset token trong file này để tránh expire time.

---

## 📝 Hướng dẫn sử dụng

1. Vào database connection để tạo connection đến database cần dùng, trong trường hợp này là trino
![database connection](./images/trino_connection.png)
2. Truy cập vào giao diện SQLLab trên Superset.
![sqllab](./images/nl2sql.png)
3. Nhập câu truy vấn dưới dạng ngôn ngữ tự nhiên (Tiếng Việt hoặc Tiếng Anh) vào thanh NL2SQL.
4. Hệ thống sẽ tự động convert sang SQL và thực thi truy vấn, trả kết quả.
5. Nếu có thay đổi gì trong database thì nhấn Refresh Schema để hệ thống cập nhật lại thông tin.
6. Nhấn Create Report sau khi đã nhấn Gen SQL để hệ thống hỗ trợ tạo báo cáo.
![create report](./images/create_report.png)
---

## 💡 Lưu ý

- Đảm bảo backend đã chạy trước khi thao tác trên giao diện Superset.
- Nếu gặp lỗi kết nối AI Model, kiểm tra lại cấu hình endpoint/key trong backend.

---

## 🤝 Đóng góp

- Mọi ý kiến đóng góp, báo lỗi hoặc đề xuất chức năng vui lòng tạo Issue hoặc Merge Request trên GitLab.

---

**Liên hệ:**  
- FPT Smart Cloud  
- Project Maintainer: Data Platform  
- Email: <EMAIL>

