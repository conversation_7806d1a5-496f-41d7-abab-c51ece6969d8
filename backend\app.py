# from flask import Flask, request, jsonify
# from flask_cors import CORS

# app = Flask(__name__)
# CORS(app)  # Cho phép gọi từ browser các port khác nhau (localhost:9000 <-> 5000)

# @app.route('/api/nl2sql', methods=['POST'])
# def nl2sql():
#     data = request.json
#     question = data.get('question', '')
#     # FAKE trả về SQL demo
#     sql = f"-- Bạn hỏi: {question}\nSELECT * FROM birth_names LIMIT 10;"
#     return jsonify({'sql': sql})

# if __name__ == '__main__':
#     app.run(port=5000, debug=True)

from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)  # Cho phép gọi từ browser các port khác nhau

@app.route('/api/nl2sql', methods=['POST'])
def nl2sql():
    data = request.json
    question = data.get('question', '')
    database = data.get('database', '')
    schema = data.get('schema', '')
    table = data.get('table', '')

    # In ra màn hình (log) toàn bộ giá trị nhận được
    print(f"Received from frontend:")
    print(f"  question: {question}")
    print(f"  database: {database}")
    print(f"  schema:   {schema}")
    print(f"  table:    {table}")

    # FAKE trả về SQL (có cả các giá trị đã nhận)
    sql = (
        f"-- Bạn hỏi: {question}\n"
        f"-- Database: {database}, Schema: {schema}, Table: {table}\n"
        f"SELECT * FROM birth_names LIMIT 10;"
    )
    return jsonify({'sql': sql})

if __name__ == '__main__':
    app.run(port=5000, debug=True)